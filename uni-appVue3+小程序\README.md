# 金舟国际物流 uni-app 应用

这是一个基于 uni-app Vue3 开发的跨平台应用，支持小程序、App 和 H5。

## 项目特点

- 🎯 **跨平台支持**：一套代码，多端运行（微信小程序、支付宝小程序、App、H5）
- 🎨 **精美界面**：与官网首页保持一致的设计风格
- 🔗 **智能跳转**：根据不同平台提供最佳的网站访问体验
- 📱 **响应式设计**：适配各种屏幕尺寸

## 功能说明

### 首页功能
- 展示金舟国际物流品牌标识和名称
- 提供"进入官网"按钮，根据平台特性智能处理：
  - **小程序**：复制官网地址到剪贴板，引导用户在浏览器中访问
  - **App**：直接打开系统浏览器访问官网
  - **H5**：在新窗口中打开官网

### 官网地址
- 主站：http://www.jinzhouil.com/

## 开发环境要求

- Node.js 16+
- HBuilderX 或 VS Code
- uni-app 开发插件

## 运行方式

### 1. 微信小程序
1. 在 HBuilderX 中打开项目
2. 点击"运行" -> "运行到小程序模拟器" -> "微信开发者工具"
3. 在微信开发者工具中预览和调试

### 2. 支付宝小程序
1. 在 HBuilderX 中打开项目
2. 点击"运行" -> "运行到小程序模拟器" -> "支付宝开发者工具"

### 3. App
1. 在 HBuilderX 中打开项目
2. 点击"运行" -> "运行到手机或模拟器"
3. 选择 Android 或 iOS 平台

### 4. H5
1. 在 HBuilderX 中打开项目
2. 点击"运行" -> "运行到浏览器"

## 项目结构

```
uni-appVue3+小程序/
├── pages/
│   └── index/
│       └── index.vue          # 首页组件
├── static/
│   └── logo.jpg              # 应用图标
├── App.vue                   # 应用入口组件
├── main.js                   # 应用入口文件
├── manifest.json             # 应用配置文件
├── pages.json               # 页面路由配置
└── uni.scss                 # 全局样式文件
```

## 配置说明

### manifest.json
- 应用基本信息配置
- 各平台特定配置
- 权限配置

### pages.json
- 页面路由配置
- 全局样式配置
- 导航栏配置

## 部署说明

### 小程序发布
1. 在对应的小程序开发者工具中点击"上传"
2. 在小程序管理后台提交审核
3. 审核通过后发布

### App 打包
1. 在 HBuilderX 中点击"发行" -> "原生App-云打包"
2. 配置签名证书和应用信息
3. 提交打包，下载安装包

### H5 部署
1. 在 HBuilderX 中点击"发行" -> "网站-H5手机版"
2. 将生成的 dist 目录上传到服务器

## 技术栈

- **框架**：uni-app
- **前端框架**：Vue 3
- **样式**：SCSS
- **构建工具**：HBuilderX / Vite

## 联系方式

如有问题，请访问官网：http://www.jinzhouil.com/
