<template>
	<view class="container">
		<image class="logo-img" src="/static/logo.jpg" mode="aspectFit"></image>
		<view class="title-area">
			<text class="main-title">
				<text class="gold">金舟</text>国际物流
			</text>
		</view>
		<view class="subtitle-area">
			<text class="subtitle">Jin Zhou International Logistics</text>
		</view>
		<view class="btn-area">
			<button class="btn" @click="enterWebsite">
				<text class="btn-text">进入官网</text>
				<text class="btn-subtext">Enter Website</text>
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		onLoad() {

		},
		methods: {
			enterWebsite() {
				// #ifdef MP-WEIXIN
				// 小程序中的处理方式
				uni.showModal({
					title: '访问官网',
					content: '小程序无法直接打开外部网站，是否复制网址到剪贴板？',
					confirmText: '复制网址',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							// 复制网址到剪贴板
							uni.setClipboardData({
								data: 'http://www.jinzhouil.com/',
								success: () => {
									uni.showModal({
										title: '复制成功',
										content: '网址已复制到剪贴板，请在浏览器中粘贴访问\n\n网址：http://www.jinzhouil.com/',
										showCancel: false,
										confirmText: '知道了'
									});
								},
								fail: () => {
									uni.showToast({
										title: '复制失败，请手动访问：http://www.jinzhouil.com/',
										icon: 'none',
										duration: 4000
									});
								}
							});
						}
					}
				});
				// #endif

				// #ifdef MP-ALIPAY
				// 支付宝小程序中的处理方式
				uni.showModal({
					title: '访问官网',
					content: '网址：http://www.jinzhouil.com/',
					confirmText: '复制网址',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							uni.setClipboardData({
								data: 'http://www.jinzhouil.com/',
								success: () => {
									uni.showToast({
										title: '网址已复制',
										icon: 'success'
									});
								}
							});
						}
					}
				});
				// #endif

				// #ifdef APP-PLUS
				// App中直接打开网页
				plus.runtime.openURL('http://www.jinzhouil.com/');
				// #endif

				// #ifdef H5
				// H5中直接跳转
				window.open('http://www.jinzhouil.com/', '_blank');
				// #endif
			}
		}
	}
</script>

<style>
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		min-height: 100vh;
		background-color: #f8f9fa;
		padding: 40rpx;
		box-sizing: border-box;
		position: relative;
	}

	.logo-img {
		width: 240rpx;
		height: 240rpx;
		margin-bottom: 40rpx;
		border-radius: 20rpx;
	}

	.title-area {
		margin-bottom: 20rpx;
	}

	.main-title {
		font-size: 96rpx;
		font-weight: bold;
		color: #0c4da2;
		text-align: center;
	}

	.gold {
		color: #D4AF37;
	}

	.subtitle-area {
		margin-bottom: 60rpx;
	}

	.subtitle {
		font-size: 48rpx;
		color: #666;
		text-align: center;
	}

	.btn-area {
		width: 100%;
		display: flex;
		justify-content: center;
	}

	.btn {
		background-color: #0c4da2;
		color: #fff;
		border: none;
		border-radius: 10rpx;
		padding: 24rpx 60rpx;
		font-size: 36rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		transition: background-color 0.3s;
		min-width: 300rpx;
	}

	.btn:active {
		background-color: #083778;
	}

	.btn-text {
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
	}

	.btn-subtext {
		font-size: 28rpx;
		opacity: 0.9;
	}
</style>
