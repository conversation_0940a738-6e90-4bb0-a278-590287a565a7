<template>
	<view class="webview-container">
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<text class="loading-text">正在加载官网...</text>
			</view>
		</view>

		<!-- 网页视图 -->
		<web-view
			:src="websiteUrl"
			@message="handleMessage"
			@load="handleLoad"
			@error="handleError">
		</web-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			websiteUrl: '',
			loading: true
		}
	},
	onLoad(options) {
		// 获取传递的URL参数
		if (options.url) {
			this.websiteUrl = decodeURIComponent(options.url);
		} else {
			// 默认网址
			this.websiteUrl = 'http://www.jinzhouil.com/';
		}

		// 设置页面标题
		uni.setNavigationBarTitle({
			title: '金舟国际物流官网'
		});

		// 显示加载提示
		uni.showLoading({
			title: '加载中...'
		});

		// 3秒后隐藏加载状态
		setTimeout(() => {
			this.loading = false;
			uni.hideLoading();
		}, 3000);
	},
	methods: {
		handleMessage(event) {
			// 处理来自web-view的消息
			console.log('收到web-view消息:', event);
		},
		handleLoad() {
			// 网页加载完成
			this.loading = false;
			uni.hideLoading();
		},
		handleError(error) {
			// 网页加载错误
			this.loading = false;
			uni.hideLoading();
			uni.showModal({
				title: '加载失败',
				content: '网页加载失败，请检查网络连接后重试',
				showCancel: false,
				confirmText: '返回',
				success: () => {
					uni.navigateBack();
				}
			});
		}
	}
}
</script>

<style>
.webview-container {
	width: 100%;
	height: 100vh;
	position: relative;
}

.loading-container {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #e0e0e0;
	border-top: 4rpx solid #0c4da2;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 20rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666;
}

web-view {
	width: 100%;
	height: 100%;
}
</style>
