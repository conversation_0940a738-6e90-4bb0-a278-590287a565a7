{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "D:/新建文件夹/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<image class=\"logo-img\" src=\"/static/logo.jpg\" mode=\"aspectFit\"></image>\r\n\t\t<view class=\"title-area\">\r\n\t\t\t<text class=\"main-title\">\r\n\t\t\t\t<text class=\"gold\">金舟</text>国际物流\r\n\t\t\t</text>\r\n\t\t</view>\r\n\t\t<view class=\"subtitle-area\">\r\n\t\t\t<text class=\"subtitle\">Jin Zhou International Logistics</text>\r\n\t\t</view>\r\n\t\t<view class=\"btn-area\">\r\n\t\t\t<button class=\"btn\" @click=\"enterWebsite\">\r\n\t\t\t\t<view class=\"btn-text\">进入官网</view>\r\n\t\t\t\t<view class=\"btn-subtext\">Enter Website</view>\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\r\n\t},\r\n\tmethods: {\r\n\t\tenterWebsite() {\r\n\t\t\t// 直接跳转到网页页面\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/webview/webview?url=' + encodeURIComponent('http://www.jinzhouil.com/')\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f8f9fa;\r\n\tpadding: 40rpx;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.logo-img {\r\n\twidth: 240rpx;\r\n\theight: 240rpx;\r\n\tmargin-bottom: 40rpx;\r\n\tborder-radius: 20rpx;\r\n}\r\n\r\n.title-area {\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.main-title {\r\n\tfont-size: 96rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #0c4da2;\r\n\ttext-align: center;\r\n}\r\n\r\n.gold {\r\n\tcolor: #D4AF37;\r\n}\r\n\r\n.subtitle-area {\r\n\tmargin-bottom: 60rpx;\r\n}\r\n\r\n.subtitle {\r\n\tfont-size: 48rpx;\r\n\tcolor: #666;\r\n\ttext-align: center;\r\n}\r\n\r\n.btn-area {\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n}\r\n\r\n.btn {\r\n\tbackground-color: #0c4da2;\r\n\tcolor: #fff;\r\n\tborder: none;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 24rpx 60rpx;\r\n\tfont-size: 36rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tmin-width: 300rpx;\r\n}\r\n\r\n.btn-text {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.btn-subtext {\r\n\tfont-size: 28rpx;\r\n\topacity: 0.9;\r\n}\r\n</style>\r\n\r\n<style>\r\n\t.container {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmin-height: 100vh;\r\n\t\tbackground-color: #f8f9fa;\r\n\t\tpadding: 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.logo-img {\r\n\t\twidth: 240rpx;\r\n\t\theight: 240rpx;\r\n\t\tmargin-bottom: 40rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t}\r\n\r\n\t.title-area {\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.main-title {\r\n\t\tfont-size: 96rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #0c4da2;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.gold {\r\n\t\tcolor: #D4AF37;\r\n\t}\r\n\r\n\t.subtitle-area {\r\n\t\tmargin-bottom: 60rpx;\r\n\t}\r\n\r\n\t.subtitle {\r\n\t\tfont-size: 48rpx;\r\n\t\tcolor: #666;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.btn-area {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.btn {\r\n\t\tbackground-color: #0c4da2;\r\n\t\tcolor: #fff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 10rpx;\r\n\t\tpadding: 24rpx 60rpx;\r\n\t\tfont-size: 36rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\ttransition: background-color 0.3s;\r\n\t\tmin-width: 300rpx;\r\n\t}\r\n\r\n\t.btn:active {\r\n\t\tbackground-color: #083778;\r\n\t}\r\n\r\n\t.btn-text {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.btn-subtext {\r\n\t\tfont-size: 28rpx;\r\n\t\topacity: 0.9;\r\n\t}\r\n</style>\r\n", "import MiniProgramPage from 'F:/abc/bbbbb/15/jinzhou/uni-appVue3+小程序/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAqBA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO,CAEP;AAAA,EACA;AAAA,EACD,SAAS;AAAA,EAER;AAAA,EACD,SAAS;AAAA,IACR,eAAe;AAEdA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,gCAAgC,mBAAmB,2BAA2B;AAAA,MACpF,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;ACrCA,GAAG,WAAW,eAAe;"}