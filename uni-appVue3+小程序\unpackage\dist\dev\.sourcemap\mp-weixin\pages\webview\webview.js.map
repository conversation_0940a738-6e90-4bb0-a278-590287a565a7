{"version": 3, "file": "webview.js", "sources": ["pages/webview/webview.vue", "D:/新建文件夹/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvd2Vidmlldy93ZWJ2aWV3LnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"webview-container\">\n\t\t<!-- 加载状态 -->\n\t\t<view v-if=\"loading\" class=\"loading-container\">\n\t\t\t<view class=\"loading-content\">\n\t\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t\t<text class=\"loading-text\">正在加载官网...</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 网页视图 -->\n\t\t<web-view\n\t\t\t:src=\"websiteUrl\"\n\t\t\t@message=\"handleMessage\"\n\t\t\t@load=\"handleLoad\"\n\t\t\t@error=\"handleError\">\n\t\t</web-view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\twebsiteUrl: '',\n\t\t\tloading: true\n\t\t}\n\t},\n\tonLoad(options) {\n\t\t// 获取传递的URL参数\n\t\tif (options.url) {\n\t\t\tthis.websiteUrl = decodeURIComponent(options.url);\n\t\t} else {\n\t\t\t// 默认网址\n\t\t\tthis.websiteUrl = 'http://www.jinzhouil.com/';\n\t\t}\n\n\t\t// 设置页面标题\n\t\tuni.setNavigationBarTitle({\n\t\t\ttitle: '金舟国际物流官网'\n\t\t});\n\n\t\t// 显示加载提示\n\t\tuni.showLoading({\n\t\t\ttitle: '加载中...'\n\t\t});\n\n\t\t// 3秒后隐藏加载状态\n\t\tsetTimeout(() => {\n\t\t\tthis.loading = false;\n\t\t\tuni.hideLoading();\n\t\t}, 3000);\n\t},\n\tmethods: {\n\t\thandleMessage(event) {\n\t\t\t// 处理来自web-view的消息\n\t\t\tconsole.log('收到web-view消息:', event);\n\t\t},\n\t\thandleLoad() {\n\t\t\t// 网页加载完成\n\t\t\tthis.loading = false;\n\t\t\tuni.hideLoading();\n\t\t},\n\t\thandleError(error) {\n\t\t\t// 网页加载错误\n\t\t\tthis.loading = false;\n\t\t\tuni.hideLoading();\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '加载失败',\n\t\t\t\tcontent: '网页加载失败，请检查网络连接后重试',\n\t\t\t\tshowCancel: false,\n\t\t\t\tconfirmText: '返回',\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style>\n.webview-container {\n\twidth: 100%;\n\theight: 100vh;\n\tposition: relative;\n}\n\n.loading-container {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tbackground-color: #f8f9fa;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tz-index: 999;\n}\n\n.loading-content {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n}\n\n.loading-spinner {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tborder: 4rpx solid #e0e0e0;\n\tborder-top: 4rpx solid #0c4da2;\n\tborder-radius: 50%;\n\tanimation: spin 1s linear infinite;\n\tmargin-bottom: 20rpx;\n}\n\n@keyframes spin {\n\t0% { transform: rotate(0deg); }\n\t100% { transform: rotate(360deg); }\n}\n\n.loading-text {\n\tfont-size: 28rpx;\n\tcolor: #666;\n}\n\nweb-view {\n\twidth: 100%;\n\theight: 100%;\n}\n</style>\n", "import MiniProgramPage from 'F:/abc/bbbbb/15/jinzhou/uni-appVue3+小程序/pages/webview/webview.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAqBA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,YAAY;AAAA,MACZ,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AAEf,QAAI,QAAQ,KAAK;AAChB,WAAK,aAAa,mBAAmB,QAAQ,GAAG;AAAA,WAC1C;AAEN,WAAK,aAAa;AAAA,IACnB;AAGAA,kBAAAA,MAAI,sBAAsB;AAAA,MACzB,OAAO;AAAA,IACR,CAAC;AAGDA,kBAAAA,MAAI,YAAY;AAAA,MACf,OAAO;AAAA,IACR,CAAC;AAGD,eAAW,MAAM;AAChB,WAAK,UAAU;AACfA,oBAAG,MAAC,YAAW;AAAA,IACf,GAAE,GAAI;AAAA,EACP;AAAA,EACD,SAAS;AAAA,IACR,cAAc,OAAO;AAEpBA,oBAAA,MAAA,MAAA,OAAA,mCAAY,iBAAiB,KAAK;AAAA,IAClC;AAAA,IACD,aAAa;AAEZ,WAAK,UAAU;AACfA,oBAAG,MAAC,YAAW;AAAA,IACf;AAAA,IACD,YAAY,OAAO;AAElB,WAAK,UAAU;AACfA,oBAAG,MAAC,YAAW;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS,MAAM;AACdA,wBAAG,MAAC,aAAY;AAAA,QACjB;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;AC7EA,GAAG,WAAW,eAAe;"}