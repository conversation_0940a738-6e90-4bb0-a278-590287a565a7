"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {};
  },
  onLoad() {
  },
  methods: {
    enterWebsite() {
      common_vendor.index.showModal({
        title: "访问官网",
        content: "是否复制网址到剪贴板？",
        confirmText: "复制网址",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.setClipboardData({
              data: "http://www.jinzhouil.com/",
              success: () => {
                common_vendor.index.showToast({
                  title: "网址已复制",
                  icon: "success"
                });
              }
            });
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_assets._imports_0,
    b: common_vendor.o((...args) => $options.enterWebsite && $options.enterWebsite(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
