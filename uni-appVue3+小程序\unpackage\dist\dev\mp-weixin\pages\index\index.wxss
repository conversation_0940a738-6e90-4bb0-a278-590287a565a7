
.container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-height: 100vh;
	background-color: #f8f9fa;
	padding: 40rpx;
	box-sizing: border-box;
}
.logo-img {
	width: 240rpx;
	height: 240rpx;
	margin-bottom: 40rpx;
	border-radius: 20rpx;
}
.title-area {
	margin-bottom: 20rpx;
}
.main-title {
	font-size: 96rpx;
	font-weight: bold;
	color: #0c4da2;
	text-align: center;
}
.gold {
	color: #D4AF37;
}
.subtitle-area {
	margin-bottom: 60rpx;
}
.subtitle {
	font-size: 48rpx;
	color: #666;
	text-align: center;
}
.btn-area {
	width: 100%;
	display: flex;
	justify-content: center;
}
.btn {
	background-color: #0c4da2;
	color: #fff;
	border: none;
	border-radius: 10rpx;
	padding: 24rpx 60rpx;
	font-size: 36rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	min-width: 300rpx;
}
.btn-text {
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}
.btn-subtext {
	font-size: 28rpx;
	opacity: 0.9;
}


.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		min-height: 100vh;
		background-color: #f8f9fa;
		padding: 40rpx;
		box-sizing: border-box;
		position: relative;
}
.logo-img {
		width: 240rpx;
		height: 240rpx;
		margin-bottom: 40rpx;
		border-radius: 20rpx;
}
.title-area {
		margin-bottom: 20rpx;
}
.main-title {
		font-size: 96rpx;
		font-weight: bold;
		color: #0c4da2;
		text-align: center;
}
.gold {
		color: #D4AF37;
}
.subtitle-area {
		margin-bottom: 60rpx;
}
.subtitle {
		font-size: 48rpx;
		color: #666;
		text-align: center;
}
.btn-area {
		width: 100%;
		display: flex;
		justify-content: center;
}
.btn {
		background-color: #0c4da2;
		color: #fff;
		border: none;
		border-radius: 10rpx;
		padding: 24rpx 60rpx;
		font-size: 36rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		transition: background-color 0.3s;
		min-width: 300rpx;
}
.btn:active {
		background-color: #083778;
}
.btn-text {
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
}
.btn-subtext {
		font-size: 28rpx;
		opacity: 0.9;
}
