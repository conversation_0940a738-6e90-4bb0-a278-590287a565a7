"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      websiteUrl: "",
      loading: true
    };
  },
  onLoad(options) {
    if (options.url) {
      this.websiteUrl = decodeURIComponent(options.url);
    } else {
      this.websiteUrl = "http://www.jinzhouil.com/";
    }
    common_vendor.index.setNavigationBarTitle({
      title: "金舟国际物流官网"
    });
    common_vendor.index.showLoading({
      title: "加载中..."
    });
    setTimeout(() => {
      this.loading = false;
      common_vendor.index.hideLoading();
    }, 3e3);
  },
  methods: {
    handleMessage(event) {
      common_vendor.index.__f__("log", "at pages/webview/webview.vue:57", "收到web-view消息:", event);
    },
    handleLoad() {
      this.loading = false;
      common_vendor.index.hideLoading();
    },
    handleError(error) {
      this.loading = false;
      common_vendor.index.hideLoading();
      common_vendor.index.showModal({
        title: "加载失败",
        content: "网页加载失败，请检查网络连接后重试",
        showCancel: false,
        confirmText: "返回",
        success: () => {
          common_vendor.index.navigateBack();
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : {}, {
    b: $data.websiteUrl,
    c: common_vendor.o((...args) => $options.handleMessage && $options.handleMessage(...args)),
    d: common_vendor.o((...args) => $options.handleLoad && $options.handleLoad(...args)),
    e: common_vendor.o((...args) => $options.handleError && $options.handleError(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/webview/webview.js.map
