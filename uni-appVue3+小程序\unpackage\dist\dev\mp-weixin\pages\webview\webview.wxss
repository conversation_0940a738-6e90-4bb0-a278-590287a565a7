
.webview-container {
	width: 100%;
	height: 100vh;
	position: relative;
}
.loading-container {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
}
.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
}
.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #e0e0e0;
	border-top: 4rpx solid #0c4da2;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 20rpx;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.loading-text {
	font-size: 28rpx;
	color: #666;
}
web-view {
	width: 100%;
	height: 100%;
}
