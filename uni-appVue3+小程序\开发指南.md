# 金舟国际物流 uni-app 开发指南

## 快速开始

### 1. 环境准备
- 安装 HBuilderX 最新版本
- 安装微信开发者工具（用于小程序开发）
- 安装支付宝开发者工具（可选）

### 2. 项目导入
1. 打开 HBuilderX
2. 文件 -> 导入 -> 从本地目录导入
3. 选择项目根目录

### 3. 运行项目

#### 微信小程序
1. 在 HBuilderX 中点击"运行" -> "运行到小程序模拟器" -> "微信开发者工具"
2. 首次运行会自动打开微信开发者工具
3. 在微信开发者工具中可以预览和调试

#### H5 网页版
1. 点击"运行" -> "运行到浏览器" -> "Chrome"
2. 会自动打开浏览器显示页面

#### App 应用
1. 点击"运行" -> "运行到手机或模拟器"
2. 选择对应的设备进行调试

## 项目特色功能

### 智能跳转处理
根据不同平台提供最佳的用户体验：

- **微信小程序**：由于小程序限制，无法直接打开外部链接，采用复制网址到剪贴板的方式
- **支付宝小程序**：同样采用复制网址的方式
- **App 应用**：直接调用系统浏览器打开网站
- **H5 网页**：在新窗口中打开网站

### 响应式设计
- 使用 rpx 单位确保在不同设备上的显示效果
- 采用 flex 布局适配各种屏幕尺寸
- 最小高度设置确保内容居中显示

## 自定义配置

### 修改网站地址
如需修改跳转的网站地址，请编辑 `pages/index/index.vue` 文件中的 `enterWebsite` 方法：

```javascript
// 将 'http://www.jinzhouil.com/' 替换为新的网址
uni.setClipboardData({
    data: '新的网址',
    // ...
});
```

### 修改应用信息
在 `manifest.json` 文件中可以修改：
- 应用名称
- 应用描述
- 版本号
- 各平台特定配置

### 修改页面标题
在 `pages.json` 文件中可以修改：
- 页面标题
- 导航栏样式
- 全局样式配置

## 发布流程

### 微信小程序发布
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 登录微信公众平台，在版本管理中提交审核
4. 审核通过后发布

### App 打包发布
1. 在 HBuilderX 中点击"发行" -> "原生App-云打包"
2. 选择打包类型（测试包/正式包）
3. 配置证书和应用信息
4. 提交打包，等待完成后下载

### H5 网页发布
1. 在 HBuilderX 中点击"发行" -> "网站-H5手机版"
2. 配置发行参数
3. 将生成的 dist 目录上传到服务器

## 常见问题

### Q: 小程序中为什么不能直接打开网站？
A: 小程序平台出于安全考虑，限制了直接跳转到外部网站的功能。我们采用复制网址的方式，引导用户在浏览器中访问。

### Q: 如何修改应用图标？
A: 替换 `static/logo.jpg` 文件，建议使用 512x512 像素的正方形图片。

### Q: 如何添加新页面？
A: 
1. 在 `pages` 目录下创建新的页面文件夹
2. 在 `pages.json` 中添加页面路径配置
3. 使用 `uni.navigateTo()` 进行页面跳转

### Q: 如何调试样式问题？
A: 
- 在 HBuilderX 中使用内置的调试工具
- 在微信开发者工具中使用调试面板
- 在浏览器中使用开发者工具

## 技术支持

如遇到技术问题，可以：
1. 查看 uni-app 官方文档：https://uniapp.dcloud.io/
2. 访问 DCloud 社区：https://ask.dcloud.net.cn/
3. 联系项目维护人员

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础的品牌展示页面
- 支持多平台智能跳转功能
- 完成响应式设计适配
